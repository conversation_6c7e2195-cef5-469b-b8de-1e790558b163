#include<iostream>
#include<vector>
using namespace std;
int main(){
    /*vector<int>num;
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.push_back(1);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.push_back(2);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.push_back(3);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.resize(5);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.resize(10);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.resize(15);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.pop_back();
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.push_back(23);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.insert(num.begin()+2,76);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.erase(num.begin()+6);
    cout<<"size of num:"<<num.size()<<endl;
    cout<<"capacity of num:"<<num.capacity()<<endl;
    


   /* num.resize(15);
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.resize(17);
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.resize(19);
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.resize(22);
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.resize(27);
    cout<<"capacity of num:"<<num.capacity()<<endl;
    num.resize(31);
    cout<<"capacity of num:"<<num.capacity()<<endl;*/
    
   


    
    
    
    

    return 0;
}