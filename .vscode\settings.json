{"C_Cpp.errorSquiggles": "disabled", "files.associations": {"iostream": "cpp", "ostream": "cpp", "vector": "cpp", "unordered_map": "cpp", "array": "cpp", "bitset": "cpp", "initializer_list": "cpp", "regex": "cpp", "utility": "cpp", "valarray": "cpp", "type_traits": "cpp", "random": "cpp", "atomic": "cpp", "*.tcc": "cpp", "cctype": "cpp", "cfenv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "complex": "cpp", "condition_variable": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cuchar": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "unordered_set": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "ratio": "cpp", "set": "cpp", "string": "cpp", "system_error": "cpp", "tuple": "cpp", "fstream": "cpp", "future": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "scoped_allocator": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp"}}