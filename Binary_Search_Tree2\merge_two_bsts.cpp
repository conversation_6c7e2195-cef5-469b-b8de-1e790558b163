// Approach1:-  o(n+m) time   o(n+m) space
// write both of their inorder traversals,
// merge these two sorted vectors into a single vector.
// now, form a balanced bst from this sorted vector :)




// Approach2:-  o(n+m) time  o(h1+h2) space.
// flatten these two trees into sorted lists, we did that..ok
// now,merge two sorted lists into a single list.
// now convert this sorted list into a balanced Bst.
